import React from 'react'

function Hotels({ trip }) {
  return (
    <div>
        <h2 className='font-bold text-xl mt-5'>Hotel Recommended Hotels</h2>
        <div className='grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4'>
            {trip?.tripData?.hotels?.map((item, index) => (
                <div key={index}>
                    <img src="/placeholder.jpg" className="w-full h-64 object-cover rounded-lg" />
                    <div className='my-2'>
                        <h2>{hotel.Name}</h2>
                </div>
            ))}
        </div>
    </div>
  )
}

export default Hotels